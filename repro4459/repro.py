# Let's try this!
from braintrust import Eval, init_dataset
import json
import os

import braintrust
from braintrust.span_identifier_v3 import SpanComponentsV3, SpanObjectTypeV3
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.trace import Status, StatusCode

def ag(msg: str) -> str:
    agent = agent_factory(cs_agent_autogen_1, [], "braintrust")
    return agent.invoke(msg)

# Create a task than run the AG agent and captures the help center article IDs
def eval_task(input: str) -> list[str]:
    str_response = ag(input)
    response = json.loads(str_response)
    return response.get("articleIds", [])


# Define a simple scorer that calculates precision and recall and something kind of like F1
# Based on https://www.braintrust.dev/docs/guides/experiments/write#adding-metadata-to-a-scoring-function
def precision_recall_score(input: str, output: list[str], expected: list[str]):
    true_positives = [item for item in output if item in expected]
    false_positives = [item for item in output if item not in expected]
    false_negatives = [item for item in expected if item not in output]
 
    precision = len(true_positives) / (len(output) or 1)
    recall = len(true_positives) / (len(expected) or 1)
 
    return {
        "name": "PrecisionRecallScore",
        "score": (precision + recall) / 2,  # F1-style simple average
        "metadata": {
            "truePositives": true_positives,
            "falsePositives": false_positives,
            "falseNegatives": false_negatives,
            "precision": precision,
            "recall": recall,
        },
    }

def span_processor(project_id: str, api_key: str) -> SpanProcessor:
    """
    Obtain an OpenTelemetry span processor given the provided Braintrust
    project ID.
    """
    otlp_exporter = OTLPSpanExporter(
        endpoint=ENDPOINT,
        headers=otlp_headers(project_id, api_key),
    )

    return BatchSpanProcessor(otlp_exporter)

def agent_factory(cs_agent_autogen_1):
    if tracer == 'opik':
        os.environ['OTEL_EXPORTER_OTLP_ENDPOINT'] = 'http://localhost:5173/api/v1/private/otel'
        # Create BatchSpanProcessor with OTLPSpanExporter
        # OTLPSpanExporter is used for exporting spans to a backend (Braintrust or Opik)
        # BatchSpanProcessor batches spans and exports them in a single request
        processor = BatchSpanProcessor(OTLPSpanExporter())
    else:
        assert tracer == 'braintrust'
        project_id = "9a892c95-d81b-43d7-952b-13a3596d9599"
        processor = braintrust_otel_setup.span_processor(project_id, api_key=os.environ.get("BRAINTRUST_API_KEY"))

    # Create a resource with service name and other metadata
    resource = Resource.create({
        "service.name": "autogen-demo",
        "service.version": "1.0.0",
        "deployment.environment": "development"
    })
    # Create TracerProvider with the resource
    # Creats and manages tracers (factories for spans)
    provider = TracerProvider(resource=resource)

    # Ensure that every span that ends goes through this processor, which then uses the exporter
    # to send the spans to the backend.
    provider.add_span_processor(processor)
    
    # Register the tracer provider globally
    # Any subsequent calls to trace.get_tracer will use this provider
    trace.set_tracer_provider(provider)

    # Instrument OpenAI calls
    OpenAIInstrumentor().instrument()

Eval(
    "project-63b5607c",
    data=init_dataset(project="project-63b5607c", name="help_article_retrieval_generated_v3"),
    task=eval_task,
    scores=[precision_recall_score,],
    experiment_name="Document retrieval precision recall take 2",
)