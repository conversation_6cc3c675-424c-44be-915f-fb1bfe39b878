import os
from braintrust import Eval, init_dataset, traced
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

# Set up OTEL to send traces to Braintrust
def setup_otel_for_experiment(experiment):
    """Set up OTEL exporter to send spans to the current experiment"""
    
    # Get your API key and API URL
    BRAINTRUST_API_KEY = os.getenv("BRAINTRUST_API_KEY")
    BRAINTRUST_API_URL = os.getenv("BRAINTRUST_API_URL", "https://api.braintrust.dev")
    
    if not BRAINTRUST_API_KEY:
        raise ValueError("BRAINTRUST_API_KEY environment variable is required")
    
    # Configure the OTLP exporter with experiment parent
    exporter = OTLPSpanExporter(
        endpoint=f"{BRAINTRUST_API_URL}/otel/v1/traces",
        headers={
            "Authorization": f"Bearer {BRAINTRUST_API_KEY}",
            "x-bt-parent": f"experiment_id:{experiment.id}",  # This is the key!
        },
    )
    
    # Set up the tracer provider
    provider = TracerProvider()
    processor = BatchSpanProcessor(exporter)
    provider.add_span_processor(processor)
    trace.set_tracer_provider(provider)
    
    return trace.get_tracer(__name__)

def otel_fake_agent(input: str) -> list[str]:
    output = ["0", "1", "2"]
    with trace.get_tracer(__name__).start_as_current_span("otel_fake_agent") as span:
        span.set_attribute("input", input)
        span.set_attribute("output", str(output))  # Convert to string for OTEL
    return output

def otel_dummy_task(input: str) -> list[str]:
    return otel_fake_agent(input)

def precision_recall_score(input: str, output: list[str], expected: list[str]):
    true_positives = [item for item in output if item in expected]
    false_positives = [item for item in output if item not in expected]
    false_negatives = [item for item in expected if item not in output]
 
    precision = len(true_positives) / (len(output) or 1)
    recall = len(true_positives) / (len(expected) or 1)
 
    return {
        "name": "PrecisionRecallScore",
        "score": (precision + recall) / 2,  # F1-style simple average
        "metadata": {
            "truePositives": true_positives,
            "falsePositives": false_positives,
            "falseNegatives": false_negatives,
            "precision": precision,
            "recall": recall,
        },
    }

# Create experiment first, then set up OTEL
experiment = Eval(
    "pedro-repro4459",
    data=init_dataset(project="project-63b5607c", name="help_article_retrieval_generated_v3"),
    task=otel_dummy_task,
    scores=[precision_recall_score,],
    experiment_name="Test with dummy task (Otel2)",
)

# Set up OTEL after creating the experiment
tracer = setup_otel_for_experiment(experiment)